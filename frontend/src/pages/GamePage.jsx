import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FaExclamationTriangle, FaGamepad } from 'react-icons/fa';
import { getGameBySlug, getGameReviews, submitGameReview, deleteGameReview, likeGame, dislikeGame, toggleFavoriteGame, getGameInteractions } from '../services/gameService';
import { useAuth } from '../context/AuthContext';
import { useNotification } from '../context/NotificationContext';
import LoadingSpinner from '../components/LoadingSpinner';
import { gamePlaceholder } from '../assets/placeholders';
import { BASE_URL } from '../config/env.js';


// Import our extracted components
import WebGameEmbed from '../components/game/WebGameEmbed';
import GameHeader from '../components/game/GameHeader';
import GameDetails from '../components/game/GameDetails';
import GameReviewSection from '../components/game/GameReviewSection';
import DeleteConfirmationModal from '../components/reviews/DeleteConfirmationModal';

/**
 * Main game page component that displays game details, web game embed, and reviews
 */
const GamePage = () => {
  const params = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showSuccess, showError } = useNotification();
  
  const [game, setGame] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [lastAction, setLastAction] = useState('');
  const [avgRating, setAvgRating] = useState(0);
  const [reviewCount, setReviewCount] = useState(0);
  
  // Add state for delete confirmation
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [reviewToDelete, setReviewToDelete] = useState(null);
  
  // Add a state to track if we should fallback to cover image if web game fails
  const [webGameError, setWebGameError] = useState(false);
  
  // Game interaction states
  const [gameInteractions, setGameInteractions] = useState({
    likes: 0,
    dislikes: 0,
    isFavorite: false,
    userLiked: false,
    userDisliked: false
  });
  const [interactionLoading, setInteractionLoading] = useState(false);
  
  // Extract the game slug from params
  const gameSlug = params.slug;
  
  // Fetch game data
  useEffect(() => {
    const fetchGameData = async () => {
      try {
        setLoading(true);

        // Fetch game details by slug
        const gameData = await getGameBySlug(gameSlug);
        setGame(gameData);

        try {
          // Fetch game reviews with user reactions if logged in (using the game ID)
          const reviewsData = await getGameReviews(gameData.id);
          setReviews(reviewsData);
        } catch (reviewError) {
          console.error('Error fetching reviews:', reviewError);
          // Continue execution even if reviews fail to load
          setReviews([]);
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching game data:', err);
        setError(err.message || 'Failed to load game data');
        setLoading(false);
      }
    };
    
    fetchGameData();
  }, [gameSlug]);
  
  // Calculate average rating when reviews change
  useEffect(() => {
    if (reviews && reviews.length > 0) {
      const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
      const average = totalRating / reviews.length;
      setAvgRating(parseFloat(average.toFixed(1))); // Round to 1 decimal place
      setReviewCount(reviews.length);
    } else {
      setAvgRating(0);
      setReviewCount(0);
    }
  }, [reviews]);
  
  // Function to handle login redirects consistently
  const redirectToLogin = (returnPath) => {
    navigate('/login', { state: { from: returnPath } });
  };
  
  // Submit review handler
  const handleSubmitReview = async (reviewData, onSuccess, onError) => {
    if (!user) {
      redirectToLogin(`/game/${game.id}`);
      return;
    }
    
    // Reset submission states
    setSubmitting(true);
    
    try {
      const reviewToSubmit = {
        title: 'Review', // Default title since we removed the title field
        comment: reviewData.content,
        rating: reviewData.rating,
        userId: user.id,
      };
      
      await submitGameReview(game.id, reviewToSubmit);
      
      // Refresh reviews after submission
      const updatedReviews = await getGameReviews(game.id);
      setReviews(updatedReviews);
      
      // Set action type
      setLastAction('submitted');
      
      // Call the success callback if provided
      if (onSuccess) onSuccess();
      
    } catch (error) {
      console.error('Failed to submit review:', error);
      // Check if this is a duplicate review error
      let errorMessage = 'Failed to submit your review. Please try again.';
      if (error.response?.status === 400 && error.response?.data?.message?.includes('already reviewed')) {
        errorMessage = 'You have already submitted a review for this game. Please edit or delete your existing review.';
      }
      
      // Call the error callback if provided
      if (onError) onError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };
  
  // Game interaction handlers
  const handleLikeGame = async () => {
    if (!user) {
      redirectToLogin(`/game/${game.id}`);
      return;
    }
    
    if (interactionLoading) return;
    
    try {
      setInteractionLoading(true);
      const result = await likeGame(game.id);
      setGameInteractions(prev => ({
        ...prev,
        likes: result.likes,
        dislikes: result.dislikes,
        userLiked: result.userLiked,
        userDisliked: result.userDisliked
      }));
    } catch (error) {
      console.error('Error liking game:', error);
    } finally {
      setInteractionLoading(false);
    }
  };
  
  const handleDislikeGame = async () => {
    if (!user) {
      redirectToLogin(`/game/${game.id}`);
      return;
    }
    
    if (interactionLoading) return;
    
    try {
      setInteractionLoading(true);
      const result = await dislikeGame(game.id);
      setGameInteractions(prev => ({
        ...prev,
        likes: result.likes,
        dislikes: result.dislikes,
        userLiked: result.userLiked,
        userDisliked: result.userDisliked
      }));
    } catch (error) {
      console.error('Error disliking game:', error);
    } finally {
      setInteractionLoading(false);
    }
  };
  
  const handleToggleFavorite = async () => {
    if (!user) {
      redirectToLogin(`/game/${game.id}`);
      return;
    }
    
    if (interactionLoading) return;
    
    try {
      setInteractionLoading(true);
      const result = await toggleFavoriteGame(game.id);
      setGameInteractions(prev => ({
        ...prev,
        isFavorite: result.isFavorite
      }));
    } catch (error) {
      console.error('Error toggling favorite:', error);
    } finally {
      setInteractionLoading(false);
    }
  };
  
  // Load game interactions when game loads
  useEffect(() => {
    const loadGameInteractions = async () => {
      if (game && game.id) {
        try {
          const interactions = await getGameInteractions(game.id);
          setGameInteractions(interactions);
        } catch (error) {
          console.error('Error loading game interactions:', error);
          // Set default values if loading fails
          setGameInteractions({
            likes: 0,
            dislikes: 0,
            isFavorite: false,
            userLiked: false,
            userDisliked: false
          });
        }
      }
    };
    
    loadGameInteractions();
  }, [game]);
  
  // Helper function to get the correct image URL
  const getCoverImageUrl = () => {
    // Helper function to build full URL from path
    const buildFullUrl = (path) => {
      if (!path) return null;
      if (path.startsWith('http://') || path.startsWith('https://')) {
        return path; // Already a full URL
      }
      // Use the centralized base URL configuration
      const baseUrl = BASE_URL;
      if (path.startsWith('/')) {
        return `${baseUrl}${path}`; // Path has leading slash
      } else {
        return `${baseUrl}/${path}`; // Add leading slash
      }
    };
    
    // Try all possible ways to get the cover image
    // 1. Check for direct coverImage property
    if (game.coverImage) {
      return buildFullUrl(game.coverImage);
    }
    
    // 2. Check for GameImages array (SQL join result)
    if (game.GameImages && Array.isArray(game.GameImages)) {
      const coverImg = game.GameImages.find(img => img.imageType === 'cover');
      if (coverImg && coverImg.filePath) {
        return buildFullUrl(coverImg.filePath);
      }
    }
    
    // 3. Check for an images array property
    if (game.images && Array.isArray(game.images)) {
      const coverImg = game.images.find(img => img.imageType === 'cover');
      if (coverImg && coverImg.filePath) {
        return buildFullUrl(coverImg.filePath);
      }
    }
    
    // 4. Check for a generic image property
    if (game.image) {
      return buildFullUrl(game.image);
    }
    
    // 5. Try a direct path based on convention
    if (game.id) {
      return buildFullUrl(`/uploads/games/${game.id}/images/cover.jpg`);
    }
    
    // Fallback to placeholder
    return gamePlaceholder;
  };
  
  // Handle initial delete request - opens confirmation dialog
  const handleDeleteRequest = (reviewId) => {
    setReviewToDelete(reviewId);
    setDeleteConfirmOpen(true);
  };
  
  // Handle confirmed deletion
  const confirmDeleteReview = async () => {
    if (!reviewToDelete) return;
    
    try {
      setSubmitting(true);
      await deleteGameReview(reviewToDelete);
      
      // Update reviews list after successful deletion
      setReviews(reviews.filter(review => review.id !== reviewToDelete));
      
      // Show success message with action type
      setLastAction('deleted');
      showSuccess('Your review has been deleted successfully! 🗑️');
    } catch (error) {
      console.error('Failed to delete review:', error);
      showError('Failed to delete your review. Please try again.');
    } finally {
      setSubmitting(false);
      setDeleteConfirmOpen(false);
      setReviewToDelete(null);
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Delete confirmation modal */}
      <DeleteConfirmationModal
        isOpen={deleteConfirmOpen}
        onCancel={() => setDeleteConfirmOpen(false)}
        onConfirm={confirmDeleteReview}
        submitting={submitting}
      />

      {loading && (
        <div className="flex flex-col items-center justify-center min-h-96">
          <LoadingSpinner size="lg" color="secondary" showText={true} text="Loading game details..." />
        </div>
      )}

      {error && (
        <div className="flex flex-col items-center justify-center min-h-96 text-center">
          <FaExclamationTriangle className="text-red-400 text-6xl mb-4" />
          <h2 className="text-white text-2xl font-bold mb-2">Error Loading Game</h2>
          <p className="text-gray-300 mb-6">{error}</p>
          <button
            onClick={() => navigate('/')}
            className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-6 py-3 rounded-lg transition-all duration-200"
          >
            Back to Home
          </button>
        </div>
      )}

      {!loading && !error && game && (
        <>
          {/* Game embed section - full width when no error */}
          {!webGameError ? (
            <div className="mb-8">
              <WebGameEmbed
                game={game}
                onError={() => setWebGameError(true)}
                gameInteractions={gameInteractions}
                interactionLoading={interactionLoading}
                onLikeGame={handleLikeGame}
                onDislikeGame={handleDislikeGame}
                onToggleFavorite={handleToggleFavorite}
                user={user}
              />
            </div>
          ) : (
            /* Fallback layout when web game fails - use grid for image and details side by side */
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div>
                  <div className="relative">
                    <img
                      src={getCoverImageUrl()}
                      alt={game.title}
                      className="w-full h-auto rounded-lg object-cover"
                      onError={(e) => {
                        e.target.onerror = null; // Prevent infinite loops
                        e.target.src = gamePlaceholder;
                      }}
                    />
                    {/* Add play button for web games when showing cover image due to error */}
                    {webGameError && game.web_game_url && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <a
                          href={game.web_game_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center gap-2"
                        >
                          <FaGamepad /> Play in new window
                        </a>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  {/* Game header information */}
                  <GameHeader
                    game={game}
                    avgRating={avgRating}
                    reviewCount={reviewCount}
                  />

                  {/* Game details information */}
                  <GameDetails game={game} />
                </div>
              </div>
            </div>
          )}

          {/* Game info and reviews section - with container for proper spacing */}
          <div className="container mx-auto px-4 pb-8">
            {/* Game info section - full width below game when no error */}
            {!webGameError && (
              <div className="mb-8">
                {/* Game header information */}
                <GameHeader
                  game={game}
                  avgRating={avgRating}
                  reviewCount={reviewCount}
                />

                {/* Game details information */}
                <GameDetails game={game} />
              </div>
            )}

            {/* Reviews section */}
            <GameReviewSection
              game={game}
              reviews={reviews}
              submitReview={handleSubmitReview}
              submitting={submitting}
              lastAction={lastAction}
              handleDeleteRequest={handleDeleteRequest}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default GamePage;
